"use client";

import { useState, useRef, useEffect } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { useToast } from "@/components/ui/use-toast";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useGroupChat } from '@/lib/hooks/useGroupChat';
import { Group } from "@/lib/types/firebase";
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface GroupChatProps {
  group: Group;
  isMember: boolean;
}

interface ChatMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderPhoto?: string;
  createdAt: Date;
}

const CodeBlock = ({ language, value }: { language: string, value: string }) => {
  return (
    <SyntaxHighlighter
      language={language}
      style={oneDark}
      className="rounded-md !my-0"
    >
      {value}
    </SyntaxHighlighter>
  );
};

export function GroupChat({ group, isMember }: GroupChatProps) {
  const { user } = useAuth();
  const { success, error: showError } = useToast();
  const { messages, loading: chatLoading, error: chatError, sendMessage: sendChatMessage } = useGroupChat(group.id);
  const [message, setMessage] = useState("");
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [shouldScroll, setShouldScroll] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const isNearBottom = () => {
    const container = messagesContainerRef.current;
    if (!container) return false;
    
    const threshold = 100; // piksel
    const position = container.scrollHeight - container.scrollTop - container.clientHeight;
    return position < threshold;
  };

  const scrollToBottom = (force = false) => {
    if ((!shouldScroll && !force) || !messagesEndRef.current) return;
    messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
  };

  const handleScroll = () => {
    setShouldScroll(isNearBottom());
  };

  useEffect(() => {
    if (messages.length > 0 && sending) {
      scrollToBottom();
    }
  }, [messages, sending]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !user) return;

    try {
      setSending(true);
      await sendChatMessage({
        content: message.trim(),
        senderPhoto: user.photoURL || ''
      });
      success('Mesaj gönderildi');
      setMessage("");
      if (textareaRef.current) {
        textareaRef.current.style.height = "44px";
        textareaRef.current.focus();
      }
      scrollToBottom(true);
    } catch (error) {
      showError('Mesaj gönderilemedi');
    } finally {
      setSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "44px"; // Minimum yükseklik
      const scrollHeight = textareaRef.current.scrollHeight;
      const maxHeight = 100; // Maksimum yükseklik

      // Yüksekliği sınırla
      const newHeight = Math.min(scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  };

  if (!isMember) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">
          Mesajları görüntülemek için gruba katılmanız gerekiyor.
        </p>
      </div>
    );
  }

  if (chatLoading) {
    return <div className="h-full flex items-center justify-center">
      <p className="text-muted-foreground">Mesajlar yükleniyor...</p>
    </div>;
  }

  if (chatError) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-red-500">
          {chatError.message === 'Bu grubun mesajlarını görüntüleme yetkiniz yok'
            ? 'Bu grubun mesajlarını görüntülemek için gruba katılmanız gerekiyor.'
            : 'Mesajlar yüklenirken bir hata oluştu'}
        </p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="border-b pb-4">
        <h2 className="font-semibold">Grup Sohbeti</h2>
      </div>

      {/* Mesaj Listesi */}
      <div 
        ref={messagesContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto py-4 space-y-4 min-h-0 max-h-[calc(100vh-340px)]"
      >
        {messages.length === 0 ? (
          <p className="text-center text-muted-foreground">Henüz mesaj yok</p>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex gap-3 ${
                msg.senderId === user?.uid ? "flex-row-reverse" : ""
              }`}
              style={{
                alignItems: 'center',
              }}
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={msg.senderPhoto || ''} alt={msg.senderName} />
                <AvatarFallback>
                  {msg.senderName.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div 
                className={`group flex flex-col ${
                  msg.senderId === user?.uid ? "items-end" : ""
                }`}
              >
                <div
                  className={`px-3 py-2 rounded-lg break-words ${
                    msg.senderId === user?.uid
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <span className="font-bold mr-1">{msg.senderName}</span>
                  <ReactMarkdown
                    components={{
                      code: ({ inline, className, children, ...props }: any) => {
                        const match = /language-(\w+)/.exec(className || '');
                        return !inline && match ? (
                          <CodeBlock
                            language={match[1]}
                            value={String(children).replace(/\n$/, '')}
                          />
                        ) : (
                          <code className="bg-background/10 rounded px-1" {...props}>
                            {children}
                          </code>
                        );
                      }
                    }}
                  >
                    {msg.content}
                  </ReactMarkdown>
                </div>
                <span className="text-xs text-muted-foreground px-2">
                  {formatDistanceToNow(msg.createdAt, {
                    addSuffix: true,
                    locale: tr,
                  })}
                </span>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Mesaj Gönderme Formu */}
      <div className="border-t bg-background pt-4 pb-0">
        <form onSubmit={handleSubmit}>
          <div className="flex gap-2">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                adjustTextareaHeight();
              }}
              onKeyDown={handleKeyDown}
              placeholder="Bir mesaj yazın..."
              className="min-h-[44px] max-h-[100px] resize-none overflow-y-auto"
              disabled={sending}
            />
            <Button type="submit" size="icon" disabled={sending || !message.trim()}>
              <Send className="h-4 w-4" />
              <span className="sr-only">Gönder</span>
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
