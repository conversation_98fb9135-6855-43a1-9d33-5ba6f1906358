"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Shield, Ban, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useModerators } from "@/lib/hooks/useModerators";
import { EditModeratorDialog } from "./edit-moderator-dialog";
import type { Moderator } from "@/lib/types/firebase";

interface ModeratorActionsProps {
  moderator: Moderator;
}

export function ModeratorActions({ moderator }: ModeratorActionsProps) {
  const { updateStatus, deleteModerator } = useModerators();
  const [showEditDialog, setShowEditDialog] = useState(false);

  const handleStatusChange = async (status: Moderator['status']) => {
    console.log('Changing moderator status:', moderator.id, status);
    try {
      await updateStatus.mutateAsync({
        moderatorId: moderator.id,
        status
      });
    } catch (error) {
      console.error('Error changing moderator status:', error);
    }
  };

  const handleDelete = async () => {
    if (confirm('Bu moderatörü silmek istediğinizden emin misiniz?')) {
      console.log('Delete moderator clicked:', moderator.id);
      try {
        await deleteModerator.mutateAsync(moderator.id);
      } catch (error) {
        console.error('Error deleting moderator:', error);
      }
    }
  };

  const handlePermissions = () => {
    console.log('Edit permissions clicked:', moderator.id);
    setShowEditDialog(true);
  };

  return (
    <>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => console.log('Dropdown trigger clicked for moderator:', moderator.id)}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handlePermissions}>
          <Shield className="mr-2 h-4 w-4" /> Yetkileri Düzenle
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange(moderator.status === 'suspended' ? 'active' : 'suspended')}
        >
          <Ban className="mr-2 h-4 w-4" />
          {moderator.status === 'suspended' ? 'Askıyı Kaldır' : 'Askıya Al'}
        </DropdownMenuItem>
        <DropdownMenuItem
          className="text-destructive"
          onClick={handleDelete}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Sil
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <EditModeratorDialog
      open={showEditDialog}
      onOpenChange={setShowEditDialog}
      moderator={moderator}
    />
  </>
  );
}