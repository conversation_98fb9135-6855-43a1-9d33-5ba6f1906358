"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Shield, Ban, Pencil } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useModerators } from "@/lib/hooks/useModerators";
import type { Moderator } from "@/lib/types/firebase";

interface ModeratorActionsProps {
  moderator: Moderator;
}

export function ModeratorActions({ moderator }: ModeratorActionsProps) {
  const { updateStatus } = useModerators();

  const handleStatusChange = async (status: Moderator['status']) => {
    console.log('Changing moderator status:', moderator.id, status);
    try {
      await updateStatus.mutateAsync({
        moderatorId: moderator.id,
        status
      });
    } catch (error) {
      console.error('Error changing moderator status:', error);
    }
  };

  const handleEdit = () => {
    console.log('Edit moderator clicked:', moderator.id);
  };

  const handlePermissions = () => {
    console.log('Edit permissions clicked:', moderator.id);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => console.log('Dropdown trigger clicked for moderator:', moderator.id)}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleEdit}>
          <Pencil className="mr-2 h-4 w-4" /> Düzenle
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handlePermissions}>
          <Shield className="mr-2 h-4 w-4" /> Yetkileri Düzenle
        </DropdownMenuItem>
        <DropdownMenuItem
          className="text-destructive"
          onClick={() => handleStatusChange(moderator.status === 'suspended' ? 'active' : 'suspended')}
        >
          <Ban className="mr-2 h-4 w-4" />
          {moderator.status === 'suspended' ? 'Askıyı Kaldır' : 'Askıya Al'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 