"use client";

import { useState, useEffect } from "react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, UserCog, ChevronDown, Filter } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { User } from "../../types/user";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { banUser, unbanUser, deleteUser, suspendUser, unsuspendUser } from "@/lib/firebase/users";
import { useToast } from "@/components/ui/use-toast";

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [sortField, setSortField] = useState<keyof User>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<User['status'] | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [suspendReason, setSuspendReason] = useState("");
  const [suspendDuration, setSuspendDuration] = useState<string>("");
  const { toast } = useToast();

  useEffect(() => {
    async function fetchUsers() {
      try {
        const usersSnapshot = await getDocs(collection(db, "users"));
        const usersData = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          email: doc.data().email || '',
          username: doc.data().username || '',
          status: doc.data().status || 'active',
          role: doc.data().role || 'user',
          nickname: doc.data().nickname,
          photoURL: doc.data().photoURL,
          createdAt: doc.data().createdAt?.toDate(),
          lastLogin: doc.data().lastLogin?.toDate(),
          banned: doc.data().banned,
          banReason: doc.data().banReason
        })) as User[];
        setUsers(usersData);
      } catch (error) {
        console.error("Error fetching users:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchUsers();
  }, []);

  const filteredUsers = users.filter(user => {
    if (statusFilter !== 'all') {
      if (statusFilter === 'banned' && !user.banned) return false;
      if (statusFilter === 'active' && (user.banned || user.status !== 'active')) return false;
      if (statusFilter === 'suspended' && user.status !== 'suspended') return false;
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        user.username?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.nickname?.toLowerCase().includes(query)
      );
    }

    return true;
  });

  const getStatusBadge = (user: User) => {
    let status = user.status;
    if (user.banned) status = 'banned';

    const statusConfig = {
      active: { color: 'bg-green-500', text: 'Aktif' },
      banned: { color: 'bg-red-500', text: 'Yasaklı' },
      suspended: { color: 'bg-yellow-500', text: 'Askıda' }
    };
    
    const config = statusConfig[status || 'active'];
    return (
      <div className="flex items-center gap-2">
        <div className={`h-2 w-2 rounded-full ${config.color}`}></div>
        <span className="text-sm">{config.text}</span>
      </div>
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(filteredUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleBulkAction = async (action: 'ban' | 'unban' | 'delete' | 'suspend' | 'unsuspend') => {
    if (!selectedUsers.length) return;

    try {
      switch (action) {
        case 'ban':
          await Promise.all(selectedUsers.map(id => banUser(id)));
          toast({ title: "Başarılı", description: `${selectedUsers.length} kullanıcı yasaklandı.` });
          break;
        case 'unban':
          await Promise.all(selectedUsers.map(id => unbanUser(id)));
          toast({ title: "Başarılı", description: `${selectedUsers.length} kullanıcının yasağı kaldırıldı.` });
          break;
        case 'suspend':
          const duration = suspendDuration && suspendDuration !== "unlimited" ? parseInt(suspendDuration) : undefined;
          await Promise.all(selectedUsers.map(id => suspendUser(id, suspendReason, duration)));
          toast({ title: "Başarılı", description: `${selectedUsers.length} kullanıcı askıya alındı.` });
          break;
        case 'unsuspend':
          await Promise.all(selectedUsers.map(id => unsuspendUser(id)));
          toast({ title: "Başarılı", description: `${selectedUsers.length} kullanıcının askısı kaldırıldı.` });
          break;
        case 'delete':
          await Promise.all(selectedUsers.map(id => deleteUser(id)));
          toast({ title: "Başarılı", description: `${selectedUsers.length} kullanıcı silindi.` });
          break;
      }
      // Kullanıcıları yeniden yükle
      await refreshUsers();
      setSelectedUsers([]);
      setSuspendReason("");
      setSuspendDuration("");
    } catch (error) {
      console.error('Bulk action error:', error);
      toast({
        title: "Hata",
        description: "İşlem sırasında bir hata oluştu.",
        variant: "destructive"
      });
    }
  };

  const refreshUsers = async () => {
    const usersSnapshot = await getDocs(collection(db, "users"));
    const usersData = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      email: doc.data().email || '',
      username: doc.data().username || '',
      status: doc.data().status || 'active',
      role: doc.data().role || 'user',
      nickname: doc.data().nickname,
      photoURL: doc.data().photoURL,
      createdAt: doc.data().createdAt?.toDate(),
      lastLogin: doc.data().lastLogin?.toDate(),
      banned: doc.data().banned,
      banReason: doc.data().banReason,
      suspendReason: doc.data().suspendReason,
      suspendedAt: doc.data().suspendedAt,
      suspendedUntil: doc.data().suspendedUntil
    })) as User[];
    setUsers(usersData);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcılar</h1>
        {selectedUsers.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <UserCog className="w-4 h-4 mr-2" />
                Toplu İşlem ({selectedUsers.length})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Yasakla
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Yasakla</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı yasaklamak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('ban')}>
                      Yasakla
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Yasaklamaları Kaldır
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Yasaklamaları Kaldır</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcının yasağını kaldırmak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('unban')}>
                      Yasağı Kaldır
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <Dialog>
                <DialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Askıya Al
                  </DropdownMenuItem>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Kullanıcıları Askıya Al</DialogTitle>
                    <DialogDescription>
                      {selectedUsers.length} kullanıcıyı askıya almak için sebep ve süre belirtin.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="suspend-reason">Askıya Alma Sebebi</Label>
                      <Textarea
                        id="suspend-reason"
                        placeholder="Askıya alma sebebini yazın..."
                        value={suspendReason}
                        onChange={(e) => setSuspendReason(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="suspend-duration">Süre (Gün)</Label>
                      <Select value={suspendDuration} onValueChange={setSuspendDuration}>
                        <SelectTrigger>
                          <SelectValue placeholder="Süre seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">Süresiz</SelectItem>
                          <SelectItem value="1">1 Gün</SelectItem>
                          <SelectItem value="3">3 Gün</SelectItem>
                          <SelectItem value="7">1 Hafta</SelectItem>
                          <SelectItem value="14">2 Hafta</SelectItem>
                          <SelectItem value="30">1 Ay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => {
                      setSuspendReason("");
                      setSuspendDuration("");
                    }}>
                      İptal
                    </Button>
                    <Button
                      onClick={() => handleBulkAction('suspend')}
                      disabled={!suspendReason.trim()}
                    >
                      Askıya Al
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Askıları Kaldır
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Askıları Kaldır</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcının askısını kaldırmak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('unsuspend')}>
                      Askıyı Kaldır
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem className="text-red-600" onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Sil
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Sil</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('delete')}>
                      Sil
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <Card className="p-4">
        <div className="flex gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Kullanıcı ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filtrele
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                Tümü
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('active')}>
                Aktif Kullanıcılar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('banned')}>
                Yasaklı Kullanıcılar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('suspended')}>
                Askıya Alınmış
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </Card>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Kullanıcı</TableHead>
              <TableHead>E-posta</TableHead>
              <TableHead>Kayıt Tarihi</TableHead>
              <TableHead>Son Giriş</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  Kullanıcı bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox 
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.photoURL} />
                        <AvatarFallback>{user.username?.[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.nickname || user.username}</p>
                        <p className="text-sm text-muted-foreground">@{user.username}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.createdAt ? formatDistanceToNow(user.createdAt, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? formatDistanceToNow(user.lastLogin, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(user)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm"onClick={() => window.location.href = `/admin/users/${user.id}`}>Detaylar</Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      <div className="flex items-center justify-between px-4 py-3 border-t">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Önceki
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => p + 1)}
            disabled={users.length < pageSize}
          >
            Sonraki
          </Button>
        </div>
        <div className="text-sm text-muted-foreground">
          Sayfa {page}
        </div>
      </div>
    </div>
  );
}