"use client";

import { useQuery } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { ModeratorActions } from "@/components/admin/moderators/moderator-actions";
import type { Moderator } from "@/lib/types/firebase";
import { useModerators } from '@/lib/hooks/useModerators';

interface ModeratorListProps {
  searchQuery: string;
}

export function ModeratorList({ searchQuery }: ModeratorListProps) {
  const { moderators, isLoading } = useModerators();

  console.log('ModeratorList - moderators:', moderators);
  console.log('ModeratorList - isLoading:', isLoading);

  const filteredModerators = moderators?.filter((mod) =>
    mod.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    mod.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  console.log('ModeratorList - filteredModerators:', filteredModerators);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Moderatör</TableHead>
          <TableHead>Durum</TableHead>
          <TableHead>Son Aktif</TableHead>
          <TableHead>Atanan Gruplar</TableHead>
          <TableHead>İşlem Sayısı</TableHead>
          <TableHead className="text-right">İşlemler</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {isLoading ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center">
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            </TableCell>
          </TableRow>
        ) : filteredModerators?.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-4">
              Moderatör bulunamadı
            </TableCell>
          </TableRow>
        ) : (
          filteredModerators?.map((moderator) => (
            <TableRow key={moderator.id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={moderator.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${moderator.id}`} />
                    <AvatarFallback>{moderator.displayName[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{moderator.displayName}</p>
                    <p className="text-sm text-muted-foreground">
                      {moderator.email}
                    </p>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    moderator.status === "active"
                      ? "default"
                      : moderator.status === "suspended"
                      ? "destructive"
                      : "secondary"
                  }
                >
                  {moderator.status === "active"
                    ? "Aktif"
                    : moderator.status === "suspended"
                    ? "Askıya Alındı"
                    : "Pasif"}
                </Badge>
              </TableCell>
              <TableCell>
                {moderator.lastActive
                  ? formatDistanceToNow(moderator.lastActive, {
                      addSuffix: true,
                      locale: tr,
                    })
                  : "-"}
              </TableCell>
              <TableCell>
                {moderator.assignedGroups?.length || 0} grup
              </TableCell>
              <TableCell>
                {/* Buraya moderatör istatistikleri eklenecek */}
                0 işlem
              </TableCell>
              <TableCell className="text-right">
                <ModeratorActions moderator={moderator} />
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
} 