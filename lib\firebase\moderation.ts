import { db } from "@/lib/firebase";
import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp 
} from "firebase/firestore";

export interface Report {
  id?: string;
  type: 'spam' | 'abuse' | 'harassment' | 'inappropriate' | 'other';
  reportedUserId: string;
  reportedUserName?: string;
  reportedBy: string;
  reporterName?: string;
  content: string;
  description?: string;
  status: 'open' | 'in_progress' | 'resolved_valid' | 'resolved_invalid' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo?: string;
  assignedToName?: string;
  createdAt: Date;
  updatedAt?: Date;
  resolvedAt?: Date;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface ModerationStats {
  pendingReports: number;
  resolvedReports: number;
  activeModerators: number;
  totalReports: number;
  reportsToday: number;
  averageResolutionTime: number;
}

export async function getReports(filters?: {
  status?: string;
  assignedTo?: string;
  type?: string;
  limit?: number;
}): Promise<Report[]> {
  try {
    let reportsQuery = query(
      collection(db, "reports"),
      orderBy("createdAt", "desc")
    );

    if (filters?.status && filters.status !== 'all') {
      reportsQuery = query(reportsQuery, where("status", "==", filters.status));
    }

    if (filters?.assignedTo) {
      reportsQuery = query(reportsQuery, where("assignedTo", "==", filters.assignedTo));
    }

    if (filters?.type && filters.type !== 'all') {
      reportsQuery = query(reportsQuery, where("type", "==", filters.type));
    }

    if (filters?.limit) {
      reportsQuery = query(reportsQuery, limit(filters.limit));
    }

    const snapshot = await getDocs(reportsQuery);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate(),
      resolvedAt: doc.data().resolvedAt?.toDate()
    })) as Report[];
  } catch (error) {
    console.error('Error getting reports:', error);
    throw error;
  }
}

export async function getModerationStats(): Promise<ModerationStats> {
  try {
    const reportsRef = collection(db, "reports");
    
    // Tüm raporları al
    const allReportsSnapshot = await getDocs(reportsRef);
    const totalReports = allReportsSnapshot.size;
    
    // Bekleyen raporlar
    const pendingQuery = query(reportsRef, where("status", "in", ["open", "in_progress"]));
    const pendingSnapshot = await getDocs(pendingQuery);
    const pendingReports = pendingSnapshot.size;
    
    // Çözülen raporlar
    const resolvedQuery = query(reportsRef, where("status", "in", ["resolved_valid", "resolved_invalid"]));
    const resolvedSnapshot = await getDocs(resolvedQuery);
    const resolvedReports = resolvedSnapshot.size;
    
    // Bugünkü raporlar
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayQuery = query(reportsRef, where("createdAt", ">=", Timestamp.fromDate(today)));
    const todaySnapshot = await getDocs(todayQuery);
    const reportsToday = todaySnapshot.size;
    
    // Aktif moderatörler (son 24 saatte aktivite gösteren)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const moderatorsRef = collection(db, "users");
    const moderatorsQuery = query(
      moderatorsRef, 
      where("role", "in", ["admin", "moderator"]),
      where("lastLogin", ">=", Timestamp.fromDate(yesterday))
    );
    const moderatorsSnapshot = await getDocs(moderatorsQuery);
    const activeModerators = moderatorsSnapshot.size;
    
    // Ortalama çözüm süresi (placeholder - gerçek hesaplama için resolved raporların tarihlerine bakılmalı)
    const averageResolutionTime = 2.5; // saat cinsinden
    
    return {
      pendingReports,
      resolvedReports,
      activeModerators,
      totalReports,
      reportsToday,
      averageResolutionTime
    };
  } catch (error) {
    console.error('Error getting moderation stats:', error);
    throw error;
  }
}

export async function updateReportStatus(
  reportId: string, 
  status: Report['status'], 
  notes?: string,
  adminId?: string
): Promise<void> {
  try {
    const updateData: any = {
      status,
      updatedAt: serverTimestamp()
    };
    
    if (notes) {
      updateData.notes = notes;
    }
    
    if (adminId) {
      updateData.lastUpdatedBy = adminId;
    }
    
    if (status.startsWith('resolved')) {
      updateData.resolvedAt = serverTimestamp();
    }
    
    await updateDoc(doc(db, "reports", reportId), updateData);
  } catch (error) {
    console.error('Error updating report status:', error);
    throw error;
  }
}

export async function assignReport(reportId: string, moderatorId: string, moderatorName?: string): Promise<void> {
  try {
    await updateDoc(doc(db, "reports", reportId), {
      assignedTo: moderatorId,
      assignedToName: moderatorName,
      status: 'in_progress',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error assigning report:', error);
    throw error;
  }
}

export async function createReport(report: Omit<Report, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, "reports"), {
      ...report,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating report:', error);
    throw error;
  }
}

// Real-time reports listener
export function subscribeToReports(
  callback: (reports: Report[]) => void,
  filters?: { status?: string; assignedTo?: string }
) {
  let reportsQuery = query(
    collection(db, "reports"),
    orderBy("createdAt", "desc"),
    limit(50)
  );

  if (filters?.status && filters.status !== 'all') {
    reportsQuery = query(reportsQuery, where("status", "==", filters.status));
  }

  if (filters?.assignedTo) {
    reportsQuery = query(reportsQuery, where("assignedTo", "==", filters.assignedTo));
  }

  return onSnapshot(reportsQuery, (snapshot) => {
    const reports = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate(),
      resolvedAt: doc.data().resolvedAt?.toDate()
    })) as Report[];
    
    callback(reports);
  });
}

export async function getModerators(): Promise<Array<{id: string, name: string, email: string, role: string}>> {
  try {
    const moderatorsRef = collection(db, "users");
    const moderatorsQuery = query(moderatorsRef, where("role", "in", ["admin", "moderator"]));
    const snapshot = await getDocs(moderatorsQuery);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      name: doc.data().username || doc.data().email,
      email: doc.data().email,
      role: doc.data().role
    }));
  } catch (error) {
    console.error('Error getting moderators:', error);
    throw error;
  }
}
