"use client";

import { useState, useEffect } from "react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Users, MoreHorizontal, Trash2, Power, PowerOff } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toggleGroupStatus, deleteGroupBulk } from "@/lib/firebase/groups";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";

export default function GroupsPage() {
  const [groups, setGroups] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    async function fetchGroups() {
      try {
        const groupsSnapshot = await getDocs(collection(db, "groups"));
        const groupsData = groupsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          isActive: doc.data().isActive ?? true
        }));
        setGroups(groupsData);
      } catch (error) {
        console.error("Error fetching groups:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchGroups();
  }, []);

  const filteredGroups = groups.filter(group =>
    group.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (!selectedGroups.length) return;

    try {
      switch (action) {
        case 'activate':
          await Promise.all(selectedGroups.map(id => toggleGroupStatus(id, true)));
          toast({ title: "Başarılı", description: `${selectedGroups.length} grup aktifleştirildi.` });
          break;
        case 'deactivate':
          await Promise.all(selectedGroups.map(id => toggleGroupStatus(id, false)));
          toast({ title: "Başarılı", description: `${selectedGroups.length} grup deaktifleştirildi.` });
          break;
        case 'delete':
          await deleteGroupBulk(selectedGroups);
          toast({ title: "Başarılı", description: `${selectedGroups.length} grup silindi.` });
          break;
      }

      // Grupları yeniden yükle
      await refreshGroups();
      setSelectedGroups([]);
    } catch (error) {
      console.error('Bulk action error:', error);
      toast({
        title: "Hata",
        description: "İşlem sırasında bir hata oluştu.",
        variant: "destructive"
      });
    }
  };

  const refreshGroups = async () => {
    const groupsSnapshot = await getDocs(collection(db, "groups"));
    const groupsData = groupsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      isActive: doc.data().isActive ?? true
    }));
    setGroups(groupsData);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedGroups(filteredGroups.map(group => group.id));
    } else {
      setSelectedGroups([]);
    }
  };

  const handleSelectGroup = (groupId: string, checked: boolean) => {
    if (checked) {
      setSelectedGroups(prev => [...prev, groupId]);
    } else {
      setSelectedGroups(prev => prev.filter(id => id !== groupId));
    }
  };

  const getStatusBadge = (group: any) => {
    if (!group.isActive) {
      return (
        <Badge variant="destructive">
          Kapalı
        </Badge>
      );
    }

    return (
      <Badge variant={group.type === 'public' ? 'default' : 'secondary'}>
        {group.type === 'public' ? 'Açık' : 'Özel'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gruplar</h1>
        <Button>
          <Users className="w-4 h-4 mr-2" />
          Yeni Grup
        </Button>
      </div>

      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Grup ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {selectedGroups.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {selectedGroups.length} grup seçildi
              </span>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Toplu İşlemler
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                    <Power className="w-4 h-4 mr-2" />
                    Seçilenleri Aktifleştir
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                    <PowerOff className="w-4 h-4 mr-2" />
                    Seçilenleri Deaktifleştir
                  </DropdownMenuItem>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem className="text-red-600" onSelect={(e) => e.preventDefault()}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Seçilenleri Sil
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Grupları Sil</AlertDialogTitle>
                        <AlertDialogDescription>
                          {selectedGroups.length} grubu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>İptal</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkAction('delete')}>
                          Sil
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </Card>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedGroups.length === filteredGroups.length && filteredGroups.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Grup Adı</TableHead>
              <TableHead>Kategori</TableHead>
              <TableHead>Platform</TableHead>
              <TableHead>Üye Sayısı</TableHead>
              <TableHead>Oluşturulma</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center">
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredGroups.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  Grup bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              filteredGroups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedGroups.includes(group.id)}
                      onCheckedChange={(checked) => handleSelectGroup(group.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{group.name}</p>
                      <p className="text-sm text-muted-foreground truncate max-w-md">
                        {group.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{group.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{group.platform}</Badge>
                  </TableCell>
                  <TableCell>{group.memberCount}</TableCell>
                  <TableCell>
                    {group.createdAt ? formatDistanceToNow(group.createdAt, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(group)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center gap-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleGroupStatus(group.id, !group.isActive).then(() => refreshGroups())}
                      >
                        {group.isActive ? (
                          <>
                            <PowerOff className="w-4 h-4 mr-1" />
                            Deaktif
                          </>
                        ) : (
                          <>
                            <Power className="w-4 h-4 mr-1" />
                            Aktif
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/admin/groups/${group.id}`}
                      >
                        Detaylar
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
}