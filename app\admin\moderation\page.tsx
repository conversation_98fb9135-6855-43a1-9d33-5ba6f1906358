"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { getReports, getModerationStats, updateReportStatus, assignReport, getModerators, subscribeToReports, Report, ModerationStats } from "@/lib/firebase/moderation";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, Shield, Eye, User<PERSON>he<PERSON>, Clock } from "lucide-react";

export default function ModerationPage() {
  const [reports, setReports] = useState<Report[]>([]);
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [moderators, setModerators] = useState<Array<{id: string, name: string, email: string, role: string}>>([]);
  const [filter, setFilter] = useState("all");
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [reviewNotes, setReviewNotes] = useState("");
  const [assignedModerator, setAssignedModerator] = useState("");
  const { toast } = useToast();

  useEffect(() => {
    fetchInitialData();

    // Real-time reports subscription
    const unsubscribe = subscribeToReports((newReports) => {
      setReports(newReports);
    }, { status: filter !== 'all' ? filter : undefined });

    return () => unsubscribe();
  }, [filter]);

  const fetchInitialData = async () => {
    try {
      const [reportsData, statsData, moderatorsData] = await Promise.all([
        getReports({ status: filter !== 'all' ? filter : undefined }),
        getModerationStats(),
        getModerators()
      ]);

      setReports(reportsData);
      setStats(statsData);
      setModerators(moderatorsData);
    } catch (error) {
      console.error('Error fetching moderation data:', error);
      toast({
        title: "Hata",
        description: "Moderasyon verileri yüklenirken bir hata oluştu.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReportAction = async (action: 'resolve_valid' | 'resolve_invalid' | 'escalate', reportId: string) => {
    try {
      const statusMap = {
        'resolve_valid': 'resolved_valid' as const,
        'resolve_invalid': 'resolved_invalid' as const,
        'escalate': 'escalated' as const
      };

      await updateReportStatus(reportId, statusMap[action], reviewNotes);
      toast({
        title: "Başarılı",
        description: "Rapor durumu güncellendi."
      });

      setSelectedReport(null);
      setReviewNotes("");
      fetchInitialData();
    } catch (error) {
      console.error('Error updating report:', error);
      toast({
        title: "Hata",
        description: "Rapor güncellenirken bir hata oluştu.",
        variant: "destructive"
      });
    }
  };

  const handleAssignReport = async (reportId: string, moderatorId: string) => {
    try {
      const moderator = moderators.find(m => m.id === moderatorId);
      await assignReport(reportId, moderatorId, moderator?.name);
      toast({
        title: "Başarılı",
        description: "Rapor moderatöre atandı."
      });
      fetchInitialData();
    } catch (error) {
      console.error('Error assigning report:', error);
      toast({
        title: "Hata",
        description: "Rapor atanırken bir hata oluştu.",
        variant: "destructive"
      });
    }
  };

  const filteredReports = reports.filter(report => {
    if (search) {
      const searchLower = search.toLowerCase();
      return (
        report.reportedUserName?.toLowerCase().includes(searchLower) ||
        report.content.toLowerCase().includes(searchLower) ||
        report.description?.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'open': { variant: 'destructive' as const, label: 'Açık' },
      'in_progress': { variant: 'default' as const, label: 'İnceleniyor' },
      'resolved_valid': { variant: 'secondary' as const, label: 'Çözüldü (Geçerli)' },
      'resolved_invalid': { variant: 'outline' as const, label: 'Çözüldü (Geçersiz)' },
      'escalated': { variant: 'destructive' as const, label: 'Yükseltildi' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      'spam': { variant: 'destructive' as const, label: 'Spam' },
      'abuse': { variant: 'destructive' as const, label: 'Taciz' },
      'harassment': { variant: 'destructive' as const, label: 'Zorbalık' },
      'inappropriate': { variant: 'secondary' as const, label: 'Uygunsuz' },
      'other': { variant: 'outline' as const, label: 'Diğer' }
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { variant: 'outline' as const, label: type };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-center justify-between mb-2">
        <h1 className="text-3xl font-bold mb-2">Moderasyon</h1>
        <Button>
          <Shield className="w-4 h-4 mr-2" />
          Moderatör Ekle
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Bekleyen Raporlar</h3>
              <p className="text-2xl font-bold mt-1">{stats?.pendingReports || 0}</p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Çözülen Raporlar</h3>
              <p className="text-2xl font-bold mt-1">{stats?.resolvedReports || 0}</p>
            </div>
            <Shield className="h-8 w-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Aktif Moderatörler</h3>
              <p className="text-2xl font-bold mt-1">{stats?.activeModerators || 0}</p>
            </div>
            <UserCheck className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Bugünkü Raporlar</h3>
              <p className="text-2xl font-bold mt-1">{stats?.reportsToday || 0}</p>
            </div>
            <Eye className="h-8 w-8 text-purple-500" />
          </div>
        </Card>
      </div>

      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Rapor ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrele" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tümü</SelectItem>
              <SelectItem value="open">Açık</SelectItem>
              <SelectItem value="in_progress">İnceleniyor</SelectItem>
              <SelectItem value="resolved_valid">Çözüldü (Geçerli)</SelectItem>
              <SelectItem value="resolved_invalid">Çözüldü (Geçersiz)</SelectItem>
              <SelectItem value="escalated">Yükseltildi</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Card>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tür</TableHead>
                <TableHead>Raporlanan</TableHead>
                <TableHead>Raporlayan</TableHead>
                <TableHead>İçerik</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4">
                    Rapor bulunamadı
                  </TableCell>
                </TableRow>
              ) : (
                filteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      {getTypeBadge(report.type)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{report.reportedUserName || report.reportedUserId}</p>
                        <p className="text-xs text-muted-foreground">ID: {report.reportedUserId}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{report.reporterName || 'Anonim'}</p>
                        <p className="text-xs text-muted-foreground">{new Date(report.createdAt).toLocaleDateString('tr-TR')}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        <p className="truncate">{report.content}</p>
                        {report.description && (
                          <p className="text-xs text-muted-foreground truncate">{report.description}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {report.assignedToName && (
                        <p className="text-xs text-muted-foreground mb-1">Atanan: {report.assignedToName}</p>
                      )}
                      {getStatusBadge(report.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center gap-2 justify-end">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedReport(report)}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              İncele
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Rapor İnceleme</DialogTitle>
                              <DialogDescription>
                                Rapor detaylarını inceleyin ve gerekli işlemi yapın.
                              </DialogDescription>
                            </DialogHeader>

                            {selectedReport && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <Label>Rapor Türü</Label>
                                    <p className="mt-1">{getTypeBadge(selectedReport.type)}</p>
                                  </div>
                                  <div>
                                    <Label>Durum</Label>
                                    <p className="mt-1">{getStatusBadge(selectedReport.status)}</p>
                                  </div>
                                  <div>
                                    <Label>Raporlanan Kullanıcı</Label>
                                    <p className="mt-1">{selectedReport.reportedUserName || selectedReport.reportedUserId}</p>
                                  </div>
                                  <div>
                                    <Label>Raporlayan</Label>
                                    <p className="mt-1">{selectedReport.reporterName || 'Anonim'}</p>
                                  </div>
                                </div>

                                <div>
                                  <Label>İçerik</Label>
                                  <p className="mt-1 p-3 bg-muted rounded">{selectedReport.content}</p>
                                </div>

                                {selectedReport.description && (
                                  <div>
                                    <Label>Açıklama</Label>
                                    <p className="mt-1 p-3 bg-muted rounded">{selectedReport.description}</p>
                                  </div>
                                )}

                                <div>
                                  <Label>Moderatör Atama</Label>
                                  <Select value={assignedModerator} onValueChange={setAssignedModerator}>
                                    <SelectTrigger className="mt-1">
                                      <SelectValue placeholder="Moderatör seçin" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {moderators.map(mod => (
                                        <SelectItem key={mod.id} value={mod.id}>
                                          {mod.name} ({mod.role})
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  {assignedModerator && (
                                    <Button
                                      size="sm"
                                      className="mt-2"
                                      onClick={() => handleAssignReport(selectedReport.id!, assignedModerator)}
                                    >
                                      Ata
                                    </Button>
                                  )}
                                </div>

                                <div>
                                  <Label htmlFor="review-notes">İnceleme Notları</Label>
                                  <Textarea
                                    id="review-notes"
                                    value={reviewNotes}
                                    onChange={(e) => setReviewNotes(e.target.value)}
                                    placeholder="İnceleme notlarınızı yazın..."
                                    className="mt-1"
                                  />
                                </div>
                              </div>
                            )}

                            <DialogFooter>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  onClick={() => selectedReport && handleReportAction('resolve_invalid', selectedReport.id!)}
                                >
                                  Geçersiz
                                </Button>
                                <Button
                                  variant="secondary"
                                  onClick={() => selectedReport && handleReportAction('escalate', selectedReport.id!)}
                                >
                                  Yükselt
                                </Button>
                                <Button
                                  onClick={() => selectedReport && handleReportAction('resolve_valid', selectedReport.id!)}
                                >
                                  Geçerli
                                </Button>
                              </div>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        )}
      </Card>
    </div>
  );
}