"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PermissionSelect } from "./permission-select";
import { useModerators } from "@/lib/hooks/useModerators";
import type { Moderator, AdminPermission } from "@/lib/types/firebase";

const formSchema = z.object({
  displayName: z.string().min(1, "İsim gereklidir"),
  email: z.string().email("Geçerli bir e-posta adresi girin"),
  permissions: z.array(z.string()).min(1, "En az bir yetki seçilmeli"),
});

interface EditModeratorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  moderator: Moderator | null;
}

export function EditModeratorDialog({ 
  open, 
  onOpenChange, 
  moderator 
}: EditModeratorDialogProps) {
  const { updateModerator } = useModerators();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      displayName: moderator?.displayName || "",
      email: moderator?.email || "",
      permissions: moderator?.permissions || [],
    },
  });

  // Reset form when moderator changes
  useEffect(() => {
    if (moderator) {
      form.reset({
        displayName: moderator.displayName,
        email: moderator.email,
        permissions: moderator.permissions,
      });
    }
  }, [moderator, form]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!moderator) return;
    
    setLoading(true);
    try {
      await updateModerator.mutateAsync({
        moderatorId: moderator.id,
        data: {
          displayName: values.displayName,
          permissions: values.permissions as AdminPermission[]
        }
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating moderator:', error);
    } finally {
      setLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Moderatör Düzenle</DialogTitle>
          <DialogDescription>
            Moderatör bilgilerini ve yetkilerini düzenleyin.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>İsim</FormLabel>
                  <FormControl>
                    <Input placeholder="Moderatör ismi" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-posta</FormLabel>
                  <FormControl>
                    <Input placeholder="E-posta adresi" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="permissions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Yetkiler</FormLabel>
                  <FormControl>
                    <PermissionSelect
                      value={field.value as AdminPermission[]}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Güncelleniyor..." : "Güncelle"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
