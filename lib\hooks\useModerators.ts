import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { moderatorService } from '@/lib/services/admin/moderators';
import { toast } from 'sonner';
import type { AdminPermission, Moderator } from '@/lib/types/firebase';

interface UpdateStatusParams {
  moderatorId: string;
  status: Moderator['status'];
}

interface UpdateModeratorParams {
  moderatorId: string;
  data: {
    displayName?: string;
    permissions?: AdminPermission[];
  };
}

export function useModerators() {
  const queryClient = useQueryClient();

  const { data: moderators, isLoading } = useQuery({
    queryKey: ['moderators'],
    queryFn: () => moderatorService.getModerators()
  });

  const { data: metrics } = useQuery({
    queryKey: ['moderator-metrics'],
    queryFn: () => moderatorService.getModeratorMetrics()
  });

  const addModerator = useMutation({
    mutationFn: ({ email, permissions }: { email: string, permissions: AdminPermission[] }) =>
      moderatorService.addModerator(email, permissions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderators'] });
      toast.success('Moderatör başarıyla eklendi');
    },
    onError: (error) => {
      toast.error('Moderatör eklenirken bir hata oluştu');
      console.error('Add moderator error:', error);
    }
  });

  const updateStatus = useMutation({
    mutationFn: ({ moderatorId, status }: UpdateStatusParams) =>
      moderatorService.updateModeratorStatus(moderatorId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderators'] });
      toast.success('Moderatör durumu güncellendi');
    },
    onError: (error) => {
      toast.error('Durum güncellenirken bir hata oluştu');
      console.error('Update status error:', error);
    }
  });

  const updateModerator = useMutation({
    mutationFn: ({ moderatorId, data }: UpdateModeratorParams) =>
      moderatorService.updateModerator(moderatorId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderators'] });
      toast.success('Moderatör başarıyla güncellendi');
    },
    onError: (error) => {
      toast.error('Moderatör güncellenirken bir hata oluştu');
      console.error('Update moderator error:', error);
    }
  });

  const deleteModerator = useMutation({
    mutationFn: (moderatorId: string) =>
      moderatorService.deleteModerator(moderatorId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderators'] });
      queryClient.invalidateQueries({ queryKey: ['moderator-metrics'] });
      toast.success('Moderatör başarıyla silindi');
    },
    onError: (error) => {
      toast.error('Moderatör silinirken bir hata oluştu');
      console.error('Delete moderator error:', error);
    }
  });

  return {
    moderators,
    metrics,
    isLoading,
    addModerator,
    updateStatus,
    updateModerator,
    deleteModerator
  };
} 