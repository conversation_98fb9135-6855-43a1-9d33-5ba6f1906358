import { db, auth } from '@/lib/firebase';
import { 
  collection, 
  query as firestoreQuery, 
  where, 
  getDocs, 
  addDoc, 
  updateDoc,
  doc,
  serverTimestamp,
  DocumentData,
  getDoc
} from 'firebase/firestore';
import type { Moderator, AdminPermission } from '@/lib/types/firebase';

export const moderatorService = {
  async getModerators() {
    try {
      console.log('Fetching moderators...');
      const moderatorsRef = collection(db, 'moderators');
      const moderatorsSnap = await getDocs(moderatorsRef);
      
      console.log('Moderators found:', moderatorsSnap.size);
      
      const moderators = moderatorsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Moderator[];

      const moderatorsWithUserData = await Promise.all(
        moderators.map(async (moderator) => {
          try {
            const userDoc = await getDoc(doc(db, 'users', moderator.userId));
            const userData = userDoc.data();
            return {
              ...moderator,
              photoURL: userData?.photoURL
            };
          } catch (error) {
            console.error('Error fetching user data for moderator:', moderator.id, error);
            return moderator;
          }
        })
      );

      console.log('Moderators with user data:', moderatorsWithUserData);
      return moderatorsWithUserData;
    } catch (error) {
      console.error('Error in getModerators:', error);
      throw error;
    }
  },

  async addModerator(email: string, permissions: AdminPermission[]) {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('Oturum açmanız gerekiyor');
      }

      const adminDoc = await getDoc(doc(db, 'superadmins', currentUser.uid));
      if (!adminDoc.exists()) {
        throw new Error('Bu işlem için yetkiniz yok');
      }

      const usersRef = collection(db, 'users');
      const userQuery = firestoreQuery(usersRef, where('email', '==', email));
      const userSnapshot = await getDocs(userQuery);

      if (userSnapshot.empty) {
        throw new Error('Kullanıcı bulunamadı');
      }

      const moderatorsRef = collection(db, 'moderators');
      const moderatorQuery = firestoreQuery(
        moderatorsRef, 
        where('userId', '==', userSnapshot.docs[0].id)
      );
      const moderatorSnapshot = await getDocs(moderatorQuery);

      if (!moderatorSnapshot.empty) {
        throw new Error('Bu kullanıcı zaten moderatör');
      }

      const userData = userSnapshot.docs[0].data();
      const moderatorData: Omit<Moderator, 'id'> = {
        userId: userSnapshot.docs[0].id,
        email,
        displayName: userData.displayName || email,
        role: 'moderator',
        status: 'active',
        permissions,
        assignedBy: currentUser.uid,
        assignedAt: new Date(),
        assignedGroups: [],
        assignedCategories: []
      };

      const docRef = await addDoc(collection(db, 'moderators'), moderatorData);
      
      await updateDoc(doc(db, 'users', userSnapshot.docs[0].id), {
        role: 'moderator',
        updatedAt: serverTimestamp()
      });

      return { id: docRef.id, ...moderatorData };
    } catch (error) {
      console.error('Error in addModerator:', error);
      throw error;
    }
  },

  async updateModeratorStatus(moderatorId: string, status: Moderator['status']) {
    try {
      const moderatorRef = doc(db, 'moderators', moderatorId);
      await updateDoc(moderatorRef, {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error in updateModeratorStatus:', error);
      throw error;
    }
  },

  async updateModerator(moderatorId: string, data: { displayName?: string; permissions?: AdminPermission[] }) {
    try {
      const moderatorRef = doc(db, 'moderators', moderatorId);
      await updateDoc(moderatorRef, {
        ...data,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error in updateModerator:', error);
      throw error;
    }
  },

  async getModeratorStats(moderatorId: string) {
    try {
      const logsRef = collection(db, 'adminLogs');
      const logsQuery = firestoreQuery(logsRef, where('adminId', '==', moderatorId));
      const snapshot = await getDocs(logsQuery);

      const stats = {
        totalActions: snapshot.size,
        resolvedReports: 0,
        bannedUsers: 0,
        lastActionAt: null as Date | null
      };

      snapshot.docs.forEach(doc => {
        const data = doc.data() as DocumentData;
        if (data.action === 'RESOLVE_REPORT') stats.resolvedReports++;
        if (data.action === 'BAN_USER') stats.bannedUsers++;
        if (!stats.lastActionAt || data.timestamp > stats.lastActionAt) {
          stats.lastActionAt = data.timestamp?.toDate() || null;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error in getModeratorStats:', error);
      throw error;
    }
  },

  async getModeratorMetrics() {
    try {
      console.log('Fetching moderator metrics...');
      const moderatorsRef = collection(db, 'moderators');
      const logsRef = collection(db, 'adminLogs');
      
      const moderatorsSnap = await getDocs(moderatorsRef);
      const activeCount = moderatorsSnap.docs.filter(
        doc => doc.data().status === 'active'
      ).length;

      console.log('Active moderators count:', activeCount);

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const logsQuery = firestoreQuery(
        logsRef,
        where('timestamp', '>=', thirtyDaysAgo)
      );
      const logsSnap = await getDocs(logsQuery);

      console.log('Admin logs count:', logsSnap.size);

      const stats = {
        activeCount,
        totalActions: logsSnap.size,
        resolvedReports: 0
      };

      logsSnap.docs.forEach(doc => {
        const data = doc.data();
        if (data.action === 'RESOLVE_REPORT') {
          stats.resolvedReports++;
        }
      });

      console.log('Moderator metrics:', stats);
      return stats;
    } catch (error) {
      console.error('Error in getModeratorMetrics:', error);
      throw error;
    }
  }
};
