"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PermissionSelect } from "./permission-select";
import { useModerators } from "@/lib/hooks/useModerators";
import type { AdminPermission } from "@/lib/types/firebase";
import { useToast } from "@/components/ui/use-toast";

const formSchema = z.object({
  email: z.string().email("Geçerli bir e-posta adresi girin"),
  permissions: z.array(z.string()).min(1, "En az bir yetki seçilmeli"),
});

interface AddModeratorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddModeratorDialog({ open, onOpenChange }: AddModeratorDialogProps) {
  const { addModerator } = useModerators();
  const { error: toast } = useToast();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      permissions: [],
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await addModerator.mutateAsync({
        email: values.email,
        permissions: values.permissions as AdminPermission[]
      });
      form.reset();
      onOpenChange(false);
    } catch (error) {
      toast(
        error instanceof Error 
        ? error.message 
        : 'Moderatör eklenirken bir hata oluştu'
      );
      console.error('Error adding moderator:', error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Yeni Moderatör Ekle</DialogTitle>
          <DialogDescription>
            Sisteme yeni bir moderatör ekleyin. Moderatör olarak eklenecek
            kullanıcının sistemde kayıtlı olması gerekmektedir.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-posta</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="permissions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Yetkiler</FormLabel>
                  <FormControl>
                    <PermissionSelect
                      value={field.value as AdminPermission[]}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                İptal
              </Button>
              <Button 
                type="submit" 
                disabled={addModerator.isPending}
              >
                {addModerator.isPending ? "Ekleniyor..." : "Ekle"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 